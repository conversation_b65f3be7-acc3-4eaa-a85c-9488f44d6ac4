import requests

def get_braintrust_dataset(project_id, dataset_name, braintrust_api_key):
    """
    Retrieves a dataset from Braintrust using the raw API key in the Authorization header.

    Parameters:
    - project_id (str): The Braintrust project ID.
    - dataset_name (str): The dataset slug.
    - braintrust_api_key (str): Your Braintrust API key.

    Returns:
    - dict: Dataset information if successful.
    - None: If the request fails.
    """
    url = f"https://api.braintrust.dev/app/dropbox/p/{project_id}/datasets/{dataset_name}"

    headers = {
      "Authorization": f"Bearer {braintrust_api_key}"
    }
    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Failed to retrieve dataset. Status code: {response.status_code}")
        print(f"Response: {response.text}")
        return None
    
get_braintrust_dataset("pedro-multimodal-mind2web-eval", "mind2web", "sk-in91pI0YSJ2ydhPAVvuJ16U33MlcE6gl0HxT5DI5tk2wsIoU")
print("bla")